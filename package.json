{"name": "mycoin", "version": "1.0.0", "description": "MyCoin - A desktop cryptocurrency wallet with blockchain implementation", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "npm run build && electron dist/main.js", "dev": "concurrently \"tsc -w\" \"wait-on dist/main.js && electron dist/main.js\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "package": "npm run build && electron-builder", "package:win": "npm run build && electron-builder --win", "package:mac": "npm run build && electron-builder --mac", "package:linux": "npm run build && electron-builder --linux"}, "keywords": ["blockchain", "cryptocurrency", "wallet", "electron", "desktop"], "author": "MyCoin Team", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "@types/jest": "^29.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.0.0", "electron": "^27.0.0", "electron-builder": "^24.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "rimraf": "^5.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0", "wait-on": "^7.0.0"}, "dependencies": {"crypto": "^1.0.1", "elliptic": "^6.5.4", "level": "^8.0.0", "ws": "^8.14.0", "express": "^4.18.0", "cors": "^2.8.5"}, "build": {"appId": "com.mycoin.wallet", "productName": "<PERSON><PERSON><PERSON><PERSON>", "directories": {"output": "release"}, "files": ["dist/**/*", "src/wallet/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "repository": {"type": "git", "url": "https://github.com/yourusername/mycoin.git"}, "bugs": {"url": "https://github.com/yourusername/mycoin/issues"}, "homepage": "https://github.com/yourusername/mycoin#readme"}