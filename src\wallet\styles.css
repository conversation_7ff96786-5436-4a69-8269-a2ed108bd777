/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  height: 100vh;
  overflow: hidden;
}

/* App Container */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Header */
.header {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo i {
  font-size: 2rem;
  color: #667eea;
}

.logo h1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.network-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff6b6b;
  animation: pulse 2s infinite;
}

.status-indicator.connected {
  background: #51cf66;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Main Content */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  width: 250px;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  padding: 2rem 0;
}

.nav-menu {
  list-style: none;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-item:hover {
  background: rgba(102, 126, 234, 0.1);
  border-left-color: #667eea;
}

.nav-item.active {
  background: rgba(102, 126, 234, 0.15);
  border-left-color: #667eea;
  color: #667eea;
}

.nav-item i {
  font-size: 1.2rem;
  width: 20px;
}

/* Content Area */
.content-area {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  background: rgba(255, 255, 255, 0.1);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Dashboard */
.dashboard-header {
  margin-bottom: 2rem;
}

.dashboard-header h2 {
  color: white;
  margin-bottom: 1rem;
  font-size: 2rem;
}

.balance-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.balance-info {
  display: flex;
  flex-direction: column;
}

.balance-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.balance-amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
}

.balance-actions {
  display: flex;
  gap: 1rem;
}

/* Stats Cards */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.stat-info {
  display: flex;
  flex-direction: column;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
}

.stat-label {
  font-size: 0.9rem;
  color: #666;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.1rem;
}

/* Forms */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: white;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Cards */
.action-card,
.wallet-info {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 1rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.action-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.action-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.action-content h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

.action-content p {
  color: #666;
  margin-bottom: 1rem;
}

/* Wallet Actions */
.wallet-actions {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

/* Current Wallet */
.current-wallet {
  display: none;
}

.current-wallet.visible {
  display: block;
}

/* Info Rows */
.info-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: #333;
}

.info-value {
  font-family: "Courier New", monospace;
  color: #666;
  word-break: break-all;
  max-width: 300px;
}

.btn-copy {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-copy:hover {
  background: rgba(102, 126, 234, 0.1);
}

/* Available Balance */
.available-balance {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 0.25rem;
}

/* Section Headers */
.wallet-header h2,
.send-header h2 {
  color: white;
  margin-bottom: 2rem;
  font-size: 2rem;
}

/* Send Form */
.send-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.send-form .form-group label {
  color: #333;
}

/* Modals */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
  z-index: 1000;
  align-items: center;
  justify-content: center;
}

.modal.active {
  display: flex;
}

.modal-content {
  background: white;
  border-radius: 15px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-header h3 {
  color: #333;
  font-size: 1.3rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Tab Buttons */
.tab-buttons {
  display: flex;
  margin-bottom: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.tab-btn {
  flex: 1;
  padding: 0.75rem;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: #667eea;
  color: white;
}

.load-method {
  display: none;
}

.load-method.active {
  display: block;
}

/* Transaction List */
.recent-transactions {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.recent-transactions h3 {
  color: #333;
  margin-bottom: 1rem;
}

.transaction-list {
  max-height: 300px;
  overflow-y: auto;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.transaction-item:hover {
  background: rgba(102, 126, 234, 0.05);
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.transaction-hash {
  font-family: "Courier New", monospace;
  font-size: 0.9rem;
  color: #666;
}

.transaction-type {
  font-size: 0.8rem;
  color: #999;
}

.transaction-amount {
  font-weight: 600;
  font-size: 1.1rem;
}

.transaction-amount.positive {
  color: #51cf66;
}

.transaction-amount.negative {
  color: #ff6b6b;
}

.no-transactions {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.no-transactions i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    padding: 1rem 0;
  }

  .nav-menu {
    display: flex;
    overflow-x: auto;
  }

  .nav-item {
    min-width: 120px;
    flex-direction: column;
    text-align: center;
    padding: 1rem;
  }

  .nav-item span {
    font-size: 0.8rem;
    margin-top: 0.25rem;
  }

  .content-area {
    padding: 1rem;
  }

  .balance-card {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
  }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.7);
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #667eea;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
    will-change: transform;
  }
}

/* Receive Tab */
.receive-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}

.qr-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.qr-placeholder {
  width: 200px;
  height: 200px;
  border: 2px dashed #ccc;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: #666;
}

.qr-placeholder i {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.address-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.address-display {
  display: flex;
  gap: 0.5rem;
}

.address-display input {
  flex: 1;
}

.receive-instructions {
  margin-top: 2rem;
}

.receive-instructions h3 {
  color: #333;
  margin-bottom: 1rem;
}

.receive-instructions ol {
  color: #666;
  padding-left: 1.5rem;
}

.receive-instructions li {
  margin-bottom: 0.5rem;
}

/* History Tab */
.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.history-header h2 {
  color: white;
  font-size: 2rem;
}

.transaction-history {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

/* Mining Tab */
.mining-content {
  margin-top: 2rem;
}

.mining-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.mining-actions {
  margin-bottom: 2rem;
}

.mining-info {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.mining-info h3 {
  color: #333;
  margin-bottom: 1rem;
}

.mining-info p {
  color: #666;
  margin-bottom: 1rem;
}

.mining-info ul {
  color: #666;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.mining-info li {
  margin-bottom: 0.5rem;
}

/* Network Tab */
.network-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.network-header h2 {
  color: white;
  font-size: 2rem;
}

.network-content {
  margin-top: 2rem;
}

.network-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.peers-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.peers-section h3 {
  color: #333;
  margin-bottom: 1rem;
}

.peers-list {
  max-height: 300px;
  overflow-y: auto;
}

.peer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.peer-item:hover {
  background: rgba(102, 126, 234, 0.05);
}

.peer-item:last-child {
  border-bottom: none;
}

.peer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.peer-url {
  font-family: "Courier New", monospace;
  font-size: 0.9rem;
  color: #333;
}

.peer-status {
  font-size: 0.8rem;
  font-weight: 600;
}

.peer-status.connected {
  color: #51cf66;
}

.peer-status.disconnected {
  color: #ff6b6b;
}

.no-peers {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.no-peers i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.network-info {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.network-info h3 {
  color: #333;
  margin-bottom: 1rem;
}

.network-info p {
  color: #666;
  margin-bottom: 1rem;
}

.network-info ul {
  color: #666;
  padding-left: 1.5rem;
}

.network-info li {
  margin-bottom: 0.5rem;
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  padding: 1rem 1.5rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 1rem;
  z-index: 2000;
  min-width: 300px;
  animation: slideIn 0.3s ease;
}

.notification.success {
  border-left: 4px solid #51cf66;
}

.notification.error {
  border-left: 4px solid #ff6b6b;
}

.notification.info {
  border-left: 4px solid #667eea;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.notification-content i {
  font-size: 1.2rem;
}

.notification.success .notification-content i {
  color: #51cf66;
}

.notification.error .notification-content i {
  color: #ff6b6b;
}

.notification.info .notification-content i {
  color: #667eea;
}

.notification-close {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
    will-change: transform, opacity;
  }
  to {
    transform: translateX(0);
    opacity: 1;
    will-change: auto;
  }
}
